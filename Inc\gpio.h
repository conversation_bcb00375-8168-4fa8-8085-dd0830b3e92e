/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.h
  * @brief   This file contains all the function prototypes for
  *          the gpio.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __GPIO_H__
#define __GPIO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */
#define LED1_OFF        HAL_GPIO_WritePin(GPIOC, LED1_Pin, GPIO_PIN_RESET)    // LED1关闭，低电平有效
#define LED1_ON         HAL_GPIO_WritePin(GPIOC, LED1_Pin, GPIO_PIN_SET)      // LED1打开，高电平有效
#define LED1_TOGGLE     HAL_GPIO_TogglePin(GPIOC, LED1_Pin)                   // LED1状态翻转

// 电源管理函数声明 - 用于解决PMOS管在高电池电压下无法彻底关闭的问题
void RF_PowerPin_SetInput(void);           // RF电源引脚设为输入模式（关闭电源）
void RF_PowerPin_SetOutputLow(void);       // RF电源引脚设为输出低电平（开启电源）
void RF_PowerPin_SetOutputHigh(void);      // RF电源引脚设为输出高电平（关闭电源）

void GPS_PowerPin_SetInput(void);          // GPS电源引脚设为输入模式（关闭电源）
void GPS_PowerPin_SetOutputLow(void);      // GPS电源引脚设为输出低电平（开启电源）
void GPS_PowerPin_SetOutputHigh(void);     // GPS电源引脚设为输出高电平（关闭电源）

void CAM_PowerPin_SetInput(void);          // 摄像头电源引脚设为输入模式（关闭电源）
void CAM_PowerPin_SetOutputLow(void);      // 摄像头电源引脚设为输出低电平（开启电源）
void CAM_PowerPin_SetOutputHigh(void);     // 摄像头电源引脚设为输出高电平（关闭电源）

void VCHK_PowerPin_SetOutputHigh(void);    // VCHK电源引脚设为输出高电平（开启AD采样）
void VCHK_PowerPin_SetOutputLow(void);     // VCHK电源引脚设为输出低电平（关闭AD采样）

void PowerPins_SetAllInput(void);          // 所有电源引脚设为输入模式（休眠前调用）
void PowerPins_InitForWakeup(void);        // 电源引脚初始化为输出模式（唤醒后调用）
void CAM_PowerPin_CheckStatus(void);       // 检查CAM_PW引脚状态（调试用）

// 保持原有宏定义，内部使用新的封装函数
#define RF_PWR_OFF      RF_PowerPin_SetInput()           // 通信模块电源关闭
#define RF_PWR_ON       RF_PowerPin_SetOutputLow()       // 通信模块电源打开

#define GPS_PWR_OFF     GPS_PowerPin_SetInput()          // GPS电源关闭
#define GPS_PWR_ON      GPS_PowerPin_SetOutputLow()      // GPS电源打开

#define CAM_PW_OFF      CAM_PowerPin_SetInput()          // 摄像头电源关闭
#define CAM_PW_ON       CAM_PowerPin_SetOutputLow()      // 摄像头电源打开

#define VCHK_ON         VCHK_PowerPin_SetOutputHigh()    // AD采样开关打开，高电平有效
#define VCHK_OFF        VCHK_PowerPin_SetOutputLow()     // AD采样开关关闭，低电平有效

#define V_OUT_ON        HAL_GPIO_WritePin(GPIOA, V_OUT_Pin, GPIO_PIN_SET)        // 外设电源打开，高电平有效
#define V_OUT_OFF       HAL_GPIO_WritePin(GPIOA, V_OUT_Pin, GPIO_PIN_RESET)      // 外设电源关闭，低电平有效
/* USER CODE END Includes */

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

void MX_GPIO_Init(void);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ GPIO_H__ */

